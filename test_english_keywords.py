#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试英文关键词搜索功能
"""

import sys
import os
sys.path.append('docker-deploy')

from web_spider import CNInfoSpider
from app import extract_keyword_context, highlight_keyword_in_text

def test_keyword_analysis():
    """测试关键词分析功能"""
    print("🧪 测试关键词分析功能")
    print("=" * 50)
    
    # 创建测试文本
    test_text = """
    公司积极推进人工智能技术的研发，与多家高校建立了产学研合作关系。
    The company is actively developing AI technology and has established R&D partnerships with universities.
    我们的创新团队专注于machine learning和deep learning技术的应用。
    公司投入大量资源进行research and development，特别是在artificial intelligence领域。
    通过产、学、研一体化模式，公司在innovation方面取得了显著进展。
    """
    
    # 测试关键词列表
    test_keywords = [
        # 中文关键词
        "人工智能",
        "产学研",
        "创新",
        # 英文关键词
        "AI",
        "R&D", 
        "machine learning",
        "innovation",
        "research",
        # 混合关键词
        "deep learning技术"
    ]
    
    print("📝 测试文本:")
    print(test_text)
    print("\n🔍 测试关键词:", test_keywords)
    print("\n" + "=" * 50)
    
    # 创建爬虫实例
    spider = CNInfoSpider()
    
    # 测试关键词统计
    print("\n📊 关键词统计结果:")
    keyword_stats = spider.analyze_keywords(test_text, test_keywords)
    for keyword, count in keyword_stats.items():
        print(f"  {keyword}: {count} 次")
    
    # 测试上下文提取
    print("\n📄 上下文提取测试:")
    for keyword in test_keywords:
        if keyword_stats.get(keyword, 0) > 0:
            print(f"\n🔍 关键词: {keyword}")
            contexts = extract_keyword_context(test_text, keyword, context_length=100)
            for i, context in enumerate(contexts, 1):
                print(f"  上下文 {i}: {context}")
    
    print("\n" + "=" * 50)
    print("✅ 测试完成")

def test_highlight_function():
    """测试高亮功能"""
    print("\n🎨 测试高亮功能")
    print("=" * 50)
    
    test_cases = [
        # 中文测试
        ("公司积极推进人工智能技术发展", "人工智能"),
        ("产、学、研合作模式", "产学研"),
        # 英文测试
        ("The company develops AI technology", "AI"),
        ("Research and Development department", "R&D"),
        ("machine learning algorithms", "machine learning"),
        # 大小写测试
        ("artificial intelligence research", "AI"),
        ("INNOVATION in technology", "innovation"),
        # 混合测试
        ("deep learning技术应用", "deep learning技术"),
    ]
    
    for text, keyword in test_cases:
        print(f"\n📝 文本: {text}")
        print(f"🔍 关键词: {keyword}")
        result = highlight_keyword_in_text(text, keyword)
        print(f"🎯 结果: {result}")

if __name__ == "__main__":
    test_keyword_analysis()
    test_highlight_function()
