#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试关键词搜索函数
"""

import re

def find_keyword_contexts_test(text, keywords, context_length=300):
    """在文本中查找包含关键词的上下文 - 支持中英文混合搜索"""
    print(f"    🔧 find_keyword_contexts调用: keywords={keywords}")

    contexts = []
    original_text = text

    print(f"    📄 原始文本长度: {len(original_text)}")

    # 搜索关键词
    for keyword in keywords:
        print(f"    🔍 搜索关键词: {keyword}")

        # 判断关键词类型
        is_chinese_keyword = bool(re.search(r'[\u4e00-\u9fa5]', keyword))
        is_english_keyword = bool(re.search(r'[a-zA-Z]', keyword))
        
        print(f"    🔍 关键词类型: 中文={is_chinese_keyword}, 英文={is_english_keyword}")

        # 查找关键词的所有位置
        positions = []
        start = 0
        
        if is_english_keyword and not is_chinese_keyword:
            # 纯英文关键词：不区分大小写搜索
            keyword_lower = keyword.lower()
            text_lower = original_text.lower()
            while True:
                pos = text_lower.find(keyword_lower, start)
                if pos == -1:
                    break
                positions.append(pos)
                start = pos + 1
        elif is_chinese_keyword and not is_english_keyword:
            # 纯中文关键词：在清理后的文本中搜索，但记录原文位置
            clean_text = re.sub(r'[^\u4e00-\u9fa5]', '', original_text)
            clean_to_original_map = []
            for i, char in enumerate(original_text):
                if re.match(r'[\u4e00-\u9fa5]', char):
                    clean_to_original_map.append(i)
            
            clean_start = 0
            while True:
                clean_pos = clean_text.find(keyword, clean_start)
                if clean_pos == -1:
                    break
                if clean_pos < len(clean_to_original_map):
                    original_pos = clean_to_original_map[clean_pos]
                    positions.append(original_pos)
                clean_start = clean_pos + 1
        else:
            # 中英文混合关键词：直接在原文中搜索
            while True:
                pos = original_text.find(keyword, start)
                if pos == -1:
                    break
                positions.append(pos)
                start = pos + 1

        print(f"    📍 关键词'{keyword}' 找到 {len(positions)} 个位置")

        # 为每个位置提取上下文
        for pos in positions:
            # 计算上下文范围
            start_pos = max(0, pos - context_length // 2)
            end_pos = min(len(original_text), pos + context_length // 2)

            context = original_text[start_pos:end_pos].strip()

            if context and len(context) > 50:  # 确保上下文有足够的内容
                # 检查这个上下文包含哪些关键词（使用相同的搜索逻辑）
                found_keywords = []
                for kw in keywords:
                    kw_is_chinese = bool(re.search(r'[\u4e00-\u9fa5]', kw))
                    kw_is_english = bool(re.search(r'[a-zA-Z]', kw))
                    
                    if kw_is_english and not kw_is_chinese:
                        # 英文关键词：不区分大小写搜索
                        if kw.lower() in context.lower():
                            found_keywords.append(kw)
                    elif kw_is_chinese and not kw_is_english:
                        # 中文关键词：在清理后的上下文中搜索
                        clean_context = re.sub(r'[^\u4e00-\u9fa5]', '', context)
                        if kw in clean_context:
                            found_keywords.append(kw)
                    else:
                        # 混合关键词：直接搜索
                        if kw in context:
                            found_keywords.append(kw)

                if found_keywords:  # 只添加包含关键词的上下文
                    context_info = {
                        'text': context,
                        'has_keywords': True,
                        'keywords_found': found_keywords,
                        'position': pos  # 记录位置用于去重
                    }
                    contexts.append(context_info)

    print(f"    📝 提取到 {len(contexts)} 个原始上下文")

    # 去重：如果两个上下文的位置很接近（小于context_length），只保留一个
    unique_contexts = []
    contexts.sort(key=lambda x: x['position'])  # 按位置排序

    for context_info in contexts:
        is_duplicate = False
        for i, existing in enumerate(unique_contexts):
            # 如果两个上下文的位置距离小于context_length的一半，认为是重复
            if abs(context_info['position'] - existing['position']) < context_length // 2:
                # 合并关键词
                unique_contexts[i]['keywords_found'] = list(set(existing['keywords_found'] + context_info['keywords_found']))
                is_duplicate = True
                break

        if not is_duplicate:
            unique_contexts.append(context_info)

    # 移除所有上下文中的position字段，不需要返回给调用者
    for context in unique_contexts:
        if 'position' in context:
            del context['position']

    print(f"    📊 去重后找到 {len(unique_contexts)} 个独特的关键词上下文")
    return unique_contexts


def test_keyword_functions():
    """测试关键词搜索函数"""
    print("🧪 测试关键词搜索函数")
    print("=" * 60)
    
    # 创建测试文本
    test_text = """
    公司积极推进人工智能技术的研发，与多家高校建立了产学研合作关系。
    The company is actively developing AI technology and has established R&D partnerships with universities.
    我们的创新团队专注于machine learning和deep learning技术的应用。
    公司投入大量资源进行research and development，特别是在artificial intelligence领域。
    通过产、学、研一体化模式，公司在innovation方面取得了显著进展。
    与腾讯公司在AI领域开展深度合作，共同推进技术创新。
    Microsoft Corporation也是我们的重要合作伙伴，在云计算和AI方面有密切协作。
    """
    
    # 测试关键词列表
    test_keywords = [
        # 中文关键词
        "人工智能",
        "产学研", 
        "创新",
        # 英文关键词
        "AI",
        "R&D",
        "machine learning", 
        "innovation",
        "research",
        # 混合关键词
        "deep learning技术"
    ]
    
    print("📝 测试文本:")
    print(test_text)
    print("\n🔍 测试关键词:", test_keywords)
    print("\n" + "=" * 60)
    
    # 测试 find_keyword_contexts 函数
    print("\n📄 测试 find_keyword_contexts 函数:")
    keyword_contexts = find_keyword_contexts_test(test_text, test_keywords)
    print(f"\n✅ 找到 {len(keyword_contexts)} 个关键词上下文:")
    for i, context in enumerate(keyword_contexts, 1):
        print(f"\n上下文 {i}:")
        print(f"  包含关键词: {context['keywords_found']}")
        print(f"  文本片段: {context['text'][:150]}...")
    
    print("\n" + "=" * 60)
    print("✅ 测试完成")

if __name__ == "__main__":
    test_keyword_functions()
