#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Spider 启动脚本 - 多线程版本
"""

import os
import sys
import argparse
import time
from spider_config import SPIDER_CONFIG

def check_dependencies():
    """检查依赖"""
    required_modules = ['requests', 'concurrent.futures']
    missing_modules = []
    
    for module in required_modules:
        try:
            __import__(module)
        except ImportError:
            missing_modules.append(module)
    
    if missing_modules:
        print(f"❌ 缺少依赖模块: {', '.join(missing_modules)}")
        print("💡 请运行: pip install requests")
        return False
    
    return True

def check_company_file(file_path):
    """检查公司列表文件"""
    if not os.path.exists(file_path):
        print(f"❌ 公司列表文件不存在: {file_path}")
        
        # 创建示例文件
        sample_companies = [
            "# 公司股票代码列表",
            "# 每行一个股票代码",
            "300454",
            "000001",
            "600036",
            "600519",
            "000858",
            "# 请添加更多股票代码..."
        ]
        
        try:
            with open(file_path, 'w', encoding='utf-8') as f:
                f.write('\n'.join(sample_companies))
            print(f"✅ 已创建示例文件: {file_path}")
            print("💡 请编辑文件添加要爬取的股票代码")
        except Exception as e:
            print(f"❌ 创建示例文件失败: {e}")
        
        return False
    
    # 检查文件内容
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            lines = f.readlines()
        
        stock_codes = [line.strip() for line in lines if line.strip() and not line.strip().startswith('#')]
        
        if not stock_codes:
            print(f"⚠️ 文件 {file_path} 中没有有效的股票代码")
            return False
        
        print(f"📊 找到 {len(stock_codes)} 个股票代码")
        return True
        
    except Exception as e:
        print(f"❌ 读取文件失败: {e}")
        return False

def main():
    """主函数"""
    parser = argparse.ArgumentParser(description='巨潮资讯网年报爬虫 - 多线程版本')
    
    parser.add_argument('--workers', '-w', 
                       type=int, 
                       default=SPIDER_CONFIG['max_workers'],
                       help=f'线程数量 (默认: {SPIDER_CONFIG["max_workers"]})')
    
    parser.add_argument('--delay-min', 
                       type=int,
                       default=SPIDER_CONFIG['delay_range'][0],
                       help=f'最小延时秒数 (默认: {SPIDER_CONFIG["delay_range"][0]})')
    
    parser.add_argument('--delay-max',
                       type=int, 
                       default=SPIDER_CONFIG['delay_range'][1],
                       help=f'最大延时秒数 (默认: {SPIDER_CONFIG["delay_range"][1]})')
    
    parser.add_argument('--company-file', '-c',
                       default=SPIDER_CONFIG['company_file'],
                       help=f'公司列表文件 (默认: {SPIDER_CONFIG["company_file"]})')
    
    parser.add_argument('--save-path', '-s',
                       default=SPIDER_CONFIG['save_path'],
                       help=f'保存路径 (默认: {SPIDER_CONFIG["save_path"]})')
    
    parser.add_argument('--single-thread',
                       action='store_true',
                       help='使用单线程模式')
    
    parser.add_argument('--dry-run',
                       action='store_true',
                       help='试运行，不实际下载')
    
    parser.add_argument('--year',
                       default=SPIDER_CONFIG['target_year'],
                       help=f'目标年份 (默认: {SPIDER_CONFIG["target_year"]})')
    
    args = parser.parse_args()
    
    print("🚀 巨潮资讯网年报爬虫 - 多线程版本")
    print("=" * 60)
    print(f"📁 公司列表文件: {args.company_file}")
    print(f"💾 保存路径: {args.save_path}")
    print(f"🔧 线程数: {args.workers if not args.single_thread else 1}")
    print(f"⏱️ 延时范围: {args.delay_min}-{args.delay_max}秒")
    print(f"📅 目标年份: {args.year}")
    print(f"🧪 试运行: {'是' if args.dry_run else '否'}")
    print("=" * 60)
    
    # 检查依赖
    if not check_dependencies():
        return
    
    # 检查公司文件
    if not check_company_file(args.company_file):
        return
    
    # 创建保存目录
    os.makedirs(args.save_path, exist_ok=True)
    
    if args.dry_run:
        print("🧪 试运行模式 - 只检查配置")
        return
    
    # 确认开始
    try:
        if not args.single_thread:
            confirm = input(f"\n是否开始多线程爬取？({args.workers}线程) [Y/n]: ").strip().lower()
        else:
            confirm = input(f"\n是否开始单线程爬取？ [Y/n]: ").strip().lower()
            
        if confirm not in ['', 'y', 'yes']:
            print("❌ 用户取消操作")
            return
    except (KeyboardInterrupt, EOFError):
        print("\n❌ 用户取消操作")
        return
    
    # 动态修改spider配置
    import spider
    spider.MAX_WORKERS = 1 if args.single_thread else args.workers
    spider.DELAY_RANGE = (args.delay_min, args.delay_max)
    spider.saving_path = args.save_path
    
    # 修改目标年份过滤
    original_download = spider.download_single_pdf
    
    def custom_download_single_pdf(pdf_info):
        title = pdf_info['announcementTitle']
        target_year = args.year
        
        # 自定义年份过滤
        if f'{target_year}年年度报告' in title and '摘要' not in title:
            return original_download(pdf_info)
        elif '年度报告' in title and '摘要' not in title:
            print(f"跳过非{target_year}年年报: {title}")
            return False
        else:
            return original_download(pdf_info)
    
    spider.download_single_pdf = custom_download_single_pdf
    
    # 运行爬虫
    try:
        print(f"\n🚀 开始爬取...")
        start_time = time.time()
        
        spider.main()
        
        end_time = time.time()
        duration = end_time - start_time
        print(f"\n⏱️ 总耗时: {duration:.1f} 秒")
        
    except KeyboardInterrupt:
        print("\n⚠️ 用户中断操作")
    except Exception as e:
        print(f"\n❌ 运行出错: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
