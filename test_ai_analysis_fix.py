#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试AI分析中英文关键词修复
"""

import sys
import os
sys.path.append('docker-deploy')

def test_ai_analysis_functions():
    """测试AI分析相关函数"""
    print("🧪 测试AI分析中英文关键词修复")
    print("=" * 60)
    
    try:
        from app import find_keyword_contexts, extract_keywords_from_context, find_related_party_contexts
        
        # 创建测试文本
        test_text = """
        公司积极推进人工智能技术的研发，与多家高校建立了产学研合作关系。
        The company is actively developing AI technology and has established R&D partnerships with universities.
        我们的创新团队专注于machine learning和deep learning技术的应用。
        公司投入大量资源进行research and development，特别是在artificial intelligence领域。
        通过产、学、研一体化模式，公司在innovation方面取得了显著进展。
        与腾讯公司在AI领域开展深度合作，共同推进技术创新。
        Microsoft Corporation也是我们的重要合作伙伴，在云计算和AI方面有密切协作。
        """
        
        # 测试关键词列表
        test_keywords = [
            # 中文关键词
            "人工智能",
            "产学研", 
            "创新",
            # 英文关键词
            "AI",
            "R&D",
            "machine learning", 
            "innovation",
            "research",
            # 混合关键词
            "deep learning技术"
        ]
        
        # 测试关联方
        test_related_parties = [
            "腾讯",
            "Microsoft",
            "Microsoft Corporation"
        ]
        
        print("📝 测试文本:")
        print(test_text)
        print("\n🔍 测试关键词:", test_keywords)
        print("\n👥 测试关联方:", test_related_parties)
        print("\n" + "=" * 60)
        
        # 测试 find_keyword_contexts 函数
        print("\n📄 测试 find_keyword_contexts 函数:")
        keyword_contexts = find_keyword_contexts(test_text, test_keywords)
        print(f"找到 {len(keyword_contexts)} 个关键词上下文:")
        for i, context in enumerate(keyword_contexts, 1):
            print(f"\n上下文 {i}:")
            print(f"  包含关键词: {context['keywords_found']}")
            print(f"  文本片段: {context['text'][:100]}...")
        
        # 测试 extract_keywords_from_context 函数
        print("\n🔍 测试 extract_keywords_from_context 函数:")
        test_context = "The company is developing AI technology and machine learning solutions for innovation."
        found_keywords = extract_keywords_from_context(test_context, test_keywords)
        print(f"测试上下文: {test_context}")
        print(f"找到的关键词: {found_keywords}")
        
        # 测试 find_related_party_contexts 函数
        print("\n👥 测试 find_related_party_contexts 函数:")
        for party in test_related_parties:
            print(f"\n搜索关联方: {party}")
            party_contexts = find_related_party_contexts(test_text, party, test_keywords)
            print(f"找到 {len(party_contexts)} 个相关上下文:")
            for i, context in enumerate(party_contexts, 1):
                print(f"  上下文 {i}: 包含关键词={context['has_keywords']}, 关键词={context['keywords_found']}")
                print(f"    文本: {context['text'][:100]}...")
        
        print("\n" + "=" * 60)
        print("✅ 测试完成")
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_ai_analysis_functions()
