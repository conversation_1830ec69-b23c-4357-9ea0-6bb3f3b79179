@echo off
echo ========================================
echo CNInfo Annual Report Crawler - Build Script
echo ========================================

echo Layout Optimization Features:
echo    - Company codes and search keywords in same row
echo    - Optimized window startup position (top-left)
echo    - Auto scroll to log section
echo    - Smart file management and history
echo    - Built-in data display window

echo.
echo Cleaning old build files...
if exist build rmdir /s /q build
if exist dist rmdir /s /q dist
if exist *.spec del *.spec

echo.
echo Starting build process...
pyinstaller ^
    --onefile ^
    --windowed ^
    --name="CNInfo_Crawler_Optimized" ^
    --add-data="README.md;." ^
    --hidden-import=customtkinter ^
    --hidden-import=tkinter ^
    --hidden-import=tkinter.ttk ^
    --hidden-import=pdfplumber ^
    --hidden-import=requests ^
    --hidden-import=xlwt ^
    --hidden-import=pandas ^
    --hidden-import=openpyxl ^
    --hidden-import=PIL ^
    --hidden-import=darkdetect ^
    --hidden-import=urllib3 ^
    --hidden-import=certifi ^
    --hidden-import=charset_normalizer ^
    --collect-all=customtkinter ^
    --collect-all=pdfplumber ^
    --noupx ^
    --clean ^
    cninfo_gui_ctk.py

echo.
if exist "dist\CNInfo_Crawler_Optimized.exe" (
    echo ========================================
    echo Build Successful!
    echo ========================================
    echo Executable file: dist\CNInfo_Crawler_Optimized.exe

    echo.
    echo Creating detailed user manual...
    
    (
    echo CNInfo Annual Report Crawler - Optimized Version User Manual
    echo.
    echo Main Features:
    echo - Modern CustomTkinter dark theme interface
    echo - Company codes and search keywords in same row layout
    echo - Smart window positioning ^(top-left startup^)
    echo - Auto scroll to log section
    echo - Built-in data table viewer
    echo - Smart file management and history records
    echo.
    echo How to Use:
    echo 1. Double-click to run the program
    echo 2. Enter stock codes on the left ^(one per line^)
    echo 3. Set search keywords on the right
    echo 4. Set time range ^(YYYY-MM-DD format^)
    echo 5. Enter statistical keywords ^(one per line^)
    echo 6. Click "Start Crawling"
    echo 7. Program will auto-scroll to log area to show progress
    echo 8. Click "View Results" to see data after completion
    echo.
    echo File Organization:
    echo - results/excel/ - Latest result files
    echo - results/history/ - All historical results
    echo   - Single company: code_companyname_timestamp/
    echo   - Multiple companies: batch_query_timestamp/
    echo.
    echo Button Functions:
    echo - Start Crawling - Launch task and scroll to log
    echo - Stop - Interrupt task
    echo - Open Result Folder - View results directory
    echo - History - View all historical results
    echo - View Results - Display data table in window
    echo - Clear Log - Clear running log
    echo.
    echo Notes:
    echo - 2024 annual reports are usually published in March-April 2025
    echo - Program has built-in delays to prevent anti-crawling
    echo - Results are automatically saved for each run
    echo - Supports CSV format data export
    echo.
    echo Version: 5.0 ^(Layout Optimized^)
    echo Updated: July 3, 2025
    ) > "dist\User_Manual_Optimized.txt"

    echo User manual created successfully
    
    echo.
    echo File Information:
    dir "dist\CNInfo_Crawler_Optimized.exe" | find ".exe"

    echo.
    echo Build completed! Main improvements:
    echo    - Left-right column layout ^(codes+search in same row^)
    echo    - Optimized window position ^(top-left startup^)
    echo    - Auto scroll to log area
    echo    - Smart file management system
    echo    - Built-in data viewer

    echo.
    set /p choice=Open result folder? (y/n):
    if /i "%choice%"=="y" start explorer "dist"
    
) else (
    echo ========================================
    echo Build Failed!
    echo ========================================
    echo Please check error messages and retry
)

echo.
pause
