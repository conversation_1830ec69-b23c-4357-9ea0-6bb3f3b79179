#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
PDF转TXT启动脚本 - 多线程版本
"""

import os
import sys
import argparse
import time
from pathlib import Path
from pdf2txt_config import PDF2TXT_CONFIG

def check_dependencies():
    """检查依赖"""
    required_modules = {
        'pdfplumber': 'pdfplumber',
        'PyPDF2': 'PyPDF2'
    }
    
    missing_modules = []
    
    for module_name, package_name in required_modules.items():
        try:
            __import__(module_name)
        except ImportError:
            missing_modules.append(package_name)
    
    if missing_modules:
        print(f"❌ 缺少依赖模块: {', '.join(missing_modules)}")
        print("💡 请运行: pip install pdfplumber PyPDF2")
        return False
    
    return True

def check_directories(pdf_dir, txt_dir):
    """检查目录"""
    # 检查PDF目录
    if not os.path.exists(pdf_dir):
        print(f"❌ PDF目录不存在: {pdf_dir}")
        return False
    
    # 检查PDF文件
    pdf_count = 0
    for root, dirs, files in os.walk(pdf_dir):
        pdf_count += len([f for f in files if f.lower().endswith('.pdf')])
    
    if pdf_count == 0:
        print(f"⚠️ PDF目录中没有找到PDF文件: {pdf_dir}")
        return False
    
    print(f"📊 找到 {pdf_count} 个PDF文件")
    
    # 创建TXT目录
    os.makedirs(txt_dir, exist_ok=True)
    print(f"📁 输出目录: {txt_dir}")
    
    return True

def estimate_processing_time(pdf_dir, workers):
    """估算处理时间"""
    pdf_count = 0
    total_size = 0
    
    for root, dirs, files in os.walk(pdf_dir):
        for file in files:
            if file.lower().endswith('.pdf'):
                pdf_count += 1
                file_path = os.path.join(root, file)
                try:
                    total_size += os.path.getsize(file_path)
                except:
                    pass
    
    if pdf_count == 0:
        return 0, 0
    
    # 估算：平均每个文件2秒，多线程效率提升
    avg_time_per_file = 2.0
    efficiency_factor = min(workers * 0.7, workers)  # 多线程效率不是线性的
    estimated_time = (pdf_count * avg_time_per_file) / efficiency_factor
    
    return pdf_count, estimated_time

def main():
    """主函数"""
    parser = argparse.ArgumentParser(description='PDF转TXT工具启动器 - 多线程版本')
    
    parser.add_argument('--pdf-dir', '-p',
                       default=PDF2TXT_CONFIG['pdf_dir'],
                       help=f'PDF文件目录 (默认: {PDF2TXT_CONFIG["pdf_dir"]})')
    
    parser.add_argument('--txt-dir', '-t',
                       default=PDF2TXT_CONFIG['txt_dir'],
                       help=f'TXT输出目录 (默认: {PDF2TXT_CONFIG["txt_dir"]})')
    
    parser.add_argument('--workers', '-w',
                       type=int,
                       default=PDF2TXT_CONFIG['max_workers'],
                       help=f'线程数量 (默认: {PDF2TXT_CONFIG["max_workers"]})')
    
    parser.add_argument('--overwrite', '-o',
                       action='store_true',
                       help='覆盖已存在的TXT文件')
    
    parser.add_argument('--recursive', '-r',
                       action='store_true',
                       help='递归处理子目录')
    
    parser.add_argument('--single-thread',
                       action='store_true',
                       help='使用单线程模式')
    
    parser.add_argument('--dry-run',
                       action='store_true',
                       help='试运行，只扫描文件不转换')
    
    parser.add_argument('--batch-size',
                       type=int,
                       default=PDF2TXT_CONFIG['chunk_size'],
                       help=f'批处理大小 (默认: {PDF2TXT_CONFIG["chunk_size"]})')
    
    parser.add_argument('--quiet', '-q',
                       action='store_true',
                       help='静默模式，减少输出')
    
    args = parser.parse_args()
    
    # 显示配置信息
    if not args.quiet:
        print("🚀 PDF转TXT工具启动器 - 多线程版本")
        print("=" * 60)
        print(f"📁 PDF目录: {args.pdf_dir}")
        print(f"📁 TXT目录: {args.txt_dir}")
        print(f"🔧 线程数: {1 if args.single_thread else args.workers}")
        print(f"🔄 覆盖模式: {'是' if args.overwrite else '否'}")
        print(f"📂 递归处理: {'是' if args.recursive else '否'}")
        print(f"📦 批处理大小: {args.batch_size}")
        print(f"🧪 试运行: {'是' if args.dry_run else '否'}")
        print("=" * 60)
    
    # 检查依赖
    if not check_dependencies():
        return 1
    
    # 检查目录
    if not check_directories(args.pdf_dir, args.txt_dir):
        return 1
    
    # 估算处理时间
    if not args.quiet:
        pdf_count, estimated_time = estimate_processing_time(args.pdf_dir, args.workers)
        if estimated_time > 0:
            print(f"⏱️ 预计处理时间: {estimated_time/60:.1f} 分钟 ({pdf_count} 个文件)")
    
    if args.dry_run:
        print("🧪 试运行模式 - 只扫描文件")
        return 0
    
    # 确认开始
    if not args.quiet:
        try:
            if args.single_thread:
                confirm = input(f"\n是否开始单线程转换？ [Y/n]: ").strip().lower()
            else:
                confirm = input(f"\n是否开始多线程转换？({args.workers}线程) [Y/n]: ").strip().lower()
            
            if confirm not in ['', 'y', 'yes']:
                print("❌ 用户取消操作")
                return 0
        except (KeyboardInterrupt, EOFError):
            print("\n❌ 用户取消操作")
            return 0
    
    # 构建命令行参数
    cmd_args = [
        '--pdf-dir', args.pdf_dir,
        '--txt-dir', args.txt_dir,
        '--workers', str(args.workers)
    ]
    
    if args.overwrite:
        cmd_args.append('--overwrite')
    
    if args.recursive:
        cmd_args.append('--recursive')
    
    if args.single_thread:
        cmd_args.append('--single-thread')
    
    # 运行转换
    try:
        print(f"\n🚀 开始转换...")
        start_time = time.time()
        
        # 导入并运行主程序
        from pdf2txt_python import main as pdf2txt_main
        result = pdf2txt_main(cmd_args)
        
        end_time = time.time()
        duration = end_time - start_time
        
        if not args.quiet:
            print(f"\n⏱️ 总耗时: {duration:.1f} 秒")
            print("🎉 转换完成！")
        
        return 0
        
    except KeyboardInterrupt:
        print("\n⚠️ 用户中断操作")
        return 1
    except Exception as e:
        print(f"\n❌ 运行出错: {e}")
        import traceback
        traceback.print_exc()
        return 1

if __name__ == "__main__":
    sys.exit(main())
